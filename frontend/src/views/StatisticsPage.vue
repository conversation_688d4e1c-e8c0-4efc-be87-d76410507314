<!--
  审核统计页面
  显示合同审核的统计数据和图表
-->
<template>
  <div class="statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">{{ isEmployee ? '我的统计' : '审核统计' }}</h1>
        <p class="page-subtitle">{{ isEmployee ? '查看您的合同提交统计数据和趋势分析' : '查看合同审核的统计数据和趋势分析' }}</p>
      </div>
      <div class="header-right">
        <el-button :loading="loading" circle @click="loadStatistics">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 法规员特殊提示 -->
    <div v-if="isLegalOfficer" class="legal-officer-notice">
      <el-card class="notice-card">
        <div class="notice-content">
          <el-icon class="notice-icon" :size="48" color="#409EFF">
            <InfoFilled />
          </el-icon>
          <div class="notice-text">
            <h3>法规员角色说明</h3>
            <p>您的角色是市局法规员，主要职责是为通过审核的合同分配合同编号。</p>
            <p>法规员专注于合同编号分配工作，不提供详细的工作统计数据。</p>
            <div class="notice-actions">
              <el-button type="primary" @click="$router.push('/contracts/pending-number')">
                <el-icon><DocumentAdd /></el-icon>
                查看待分配编号合同
              </el-button>
              <el-button @click="$router.push('/contracts/number-management')">
                <el-icon><Management /></el-icon>
                编号管理
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div v-if="!isLegalOfficer" class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon :size="24"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ totalContracts }}</div>
                <div class="stats-label">{{ isEmployee ? '总提交数' : '总合同数' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon pending">
                <el-icon :size="24"><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ pendingContracts }}</div>
                <div class="stats-label">待审核</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon approved">
                <el-icon :size="24"><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ approvedContracts }}</div>
                <div class="stats-label">{{ isEmployee ? '已完成' : '已通过' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rejected">
                <el-icon :size="24"><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ rejectedContracts }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div v-if="!isLegalOfficer" class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="trendChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>分布</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="distributionChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div v-if="!isLegalOfficer" class="detailed-stats">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细统计</span>
            <el-button type="primary" size="small">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </template>

        <el-table :data="detailedStats" style="width: 100%">
          <el-table-column prop="period" label="时间段" width="120" />
          <el-table-column prop="total" label="总数" width="100" />
          <el-table-column prop="pending" label="待审核" width="100" />
          <el-table-column prop="approved" label="已通过" width="100" />
          <el-table-column prop="rejected" label="已拒绝" width="100" />
          <el-table-column prop="efficiency" label="审核效率" width="120">
            <template #default="scope">
              <el-tag :type="getEfficiencyType(scope.row.efficiency)">
                {{ scope.row.efficiency }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="avgTime" label="平均用时" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import * as echarts from "echarts";
import VChart from "vue-echarts";
import {
  Document,
  Clock,
  Check,
  Close,
  Download,
  InfoFilled,
  DocumentAdd,
  Management,
  Refresh,
} from "@element-plus/icons-vue";
import { statisticsAPI } from "@/api/statistics";
import { contractsAPI } from "@/api/contracts";
import { ElMessage } from "element-plus";
import { useAuth } from "@/composables/useAuth";

// 将ECharts添加到全局作用域
window.echarts = echarts;

// 获取用户信息
const { userRole, user, isAuthenticated } = useAuth();

// 直接从localStorage获取用户信息作为备用方案
const getUserRoleFromStorage = () => {
  try {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      const userObj = JSON.parse(savedUser);
      return userObj.role;
    }
  } catch (error) {
    console.warn('获取本地用户信息失败:', error);
  }
  return null;
};

// 检查用户角色
const currentUserRole = computed(() => userRole.value || getUserRoleFromStorage());
const isLegalOfficer = computed(() => currentUserRole.value === 'legal_officer');
const isEmployee = computed(() => currentUserRole.value === 'employee');

// 添加调试日志
console.log('👤 StatisticsPage - useAuth用户角色:', userRole.value);
console.log('👤 StatisticsPage - localStorage用户角色:', getUserRoleFromStorage());
console.log('👤 StatisticsPage - 最终用户角色:', currentUserRole.value);
console.log('👤 StatisticsPage - 是否为员工:', isEmployee.value);
console.log('👤 StatisticsPage - 用户对象:', user.value);
console.log('👤 StatisticsPage - 是否已认证:', isAuthenticated.value);

// 响应式数据
const loading = ref(false);
const chartsLoading = ref(false);
const totalContracts = ref(0);
const pendingContracts = ref(0);
const approvedContracts = ref(0);
const rejectedContracts = ref(0);

// 图表配置
const trendChartOption = ref(null);
const distributionChartOption = ref(null);

const detailedStats = ref([]);

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true;
    chartsLoading.value = true;

    // 等待用户信息加载完成
    if (!user.value && isAuthenticated.value) {
      console.log('👤 等待用户信息加载...');
      // 等待一小段时间让用户信息加载
      await new Promise(resolve => setTimeout(resolve, 100));

      // 如果还是没有用户信息，再等待一下
      if (!user.value) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      console.log('👤 用户信息加载完成:', user.value);
      console.log('👤 最终用户角色:', userRole.value);
    }

    // 如果是法规员，显示特殊提示
    if (isLegalOfficer.value) {
      ElMessage.info('法规员专注于合同编号分配工作，不提供详细统计数据');
      loading.value = false;
      chartsLoading.value = false;
      return;
    }

    // 获取汇总数据
    let summaryResponse;
    if (isEmployee.value) {
      // 员工使用合同统计API
      summaryResponse = await contractsAPI.getStats();
      if (summaryResponse.success) {
        const data = summaryResponse.data;
        totalContracts.value = data.total || 0;
        pendingContracts.value = (data.statusBreakdown?.pending || 0) +
                                (data.statusBreakdown?.pending_city_review || 0) +
                                (data.statusBreakdown?.pending_contract_number || 0);
        approvedContracts.value = (data.statusBreakdown?.approved || 0) +
                                 (data.statusBreakdown?.completed || 0);
        rejectedContracts.value = data.statusBreakdown?.rejected || 0;
      }
    } else {
      // 审核员使用审核统计API
      summaryResponse = await statisticsAPI.getSummary();
      if (summaryResponse.success) {
        const data = summaryResponse.data.overview;
        totalContracts.value = data.totalAssigned || data.totalContracts || 0;
        pendingContracts.value = data.pending || data.pendingReview || 0;
        approvedContracts.value = data.approved || 0;
        rejectedContracts.value = data.rejected || 0;
      }
    }

    // 获取图表数据（仅对审核员）
    if (!isEmployee.value) {
      // 获取趋势数据
      const trendsResponse = await statisticsAPI.getTrends();
      if (trendsResponse.success) {
        setupTrendChart(trendsResponse.data);
      }

      // 获取分布数据
      const distributionResponse = await statisticsAPI.getDistribution();
      if (distributionResponse.success) {
        setupDistributionChart(distributionResponse.data);
      }
    } else {
      // 员工显示简化的图表，但也使用真实数据
      const employeeTrendsResponse = await statisticsAPI.getTrends();
      if (employeeTrendsResponse.success) {
        setupEmployeeCharts(employeeTrendsResponse.data);
      } else {
        setupEmployeeCharts();
      }
    }

    // 设置详细统计数据
    const weeklyAvgTime = summaryResponse.data.weeklyAvgTime || "暂无数据";
    const monthlyAvgTime = summaryResponse.data.monthlyAvgTime || "暂无数据";

    detailedStats.value = [
      {
        period: "本周",
        total: totalContracts.value,
        pending: pendingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: weeklyAvgTime,
      },
      {
        period: "本月",
        total: totalContracts.value,
        pending: pendingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: monthlyAvgTime,
      },
    ];
  } catch (error) {
    console.error("加载统计数据失败:", error);
    ElMessage.error("加载统计数据失败");
  } finally {
    loading.value = false;
    chartsLoading.value = false;
  }
};

// 配置趋势图表
const setupTrendChart = (data) => {
  console.log("📊 设置趋势图表，原始数据:", data);

  // 后端直接返回trends数组，不是包装在trends属性中
  const trendsArray = Array.isArray(data) ? data : (data.trends || []);
  console.log("📊 处理后的趋势数组:", trendsArray);

  const dates = trendsArray.map((item) => item.date || item.month || item.week);
  const totals = trendsArray.map((item) => item.total || 0);
  const completed = trendsArray.map((item) => item.approved || 0);

  console.log("📊 图表数据:", { dates, totals, completed });
  console.log("📊 dates详细:", dates);
  console.log("📊 totals详细:", totals);
  console.log("📊 completed详细:", completed);

  // 如果没有数据，显示空状态
  if (dates.length === 0) {
    trendChartOption.value = {
      title: {
        text: "审核趋势",
        left: "center",
        textStyle: {
          fontSize: 16,
          color: "#333",
        },
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999'
        }
      }
    };
    return;
  }

  // 创建完整的图表配置
  const chartConfig = {
    title: {
      text: "审核趋势",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["总数", "已通过"],
      bottom: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: dates,
    },
    yAxis: {
      type: "value",
      minInterval: 1,
    },
    series: [
      {
        name: "总数",
        type: "line",
        data: totals,
        itemStyle: {
          color: "#409eff",
        },
      },
      {
        name: "已通过",
        type: "line",
        data: completed,
        itemStyle: {
          color: "#67c23a",
        },
      },
    ],
  };

  console.log("📊 最终图表配置:", chartConfig);
  console.log("📊 设置图表选项...");
  trendChartOption.value = chartConfig;

  // 强制触发响应式更新
  setTimeout(() => {
    console.log("📊 延迟检查图表实例...");
    const charts = document.querySelectorAll('.chart');
    charts.forEach((chart, index) => {
      if (chart.__echarts__) {
        console.log(`📊 图表 ${index} 实例存在，尝试重新渲染`);
        chart.__echarts__.resize();
      } else {
        console.log(`📊 图表 ${index} 实例不存在`);
      }
    });
  }, 1000);
};

// 配置分布图表
const setupDistributionChart = (data) => {
  console.log("🥧 设置分布图表，原始数据:", data);

  const distributionData = data.distribution || [
    { name: "待审核", value: pendingContracts.value },
    { name: "已通过", value: approvedContracts.value },
    { name: "已拒绝", value: rejectedContracts.value },
  ];

  console.log("🥧 分布图表数据:", distributionData);

  // 过滤掉值为0的数据项，避免显示空的扇形
  const filteredData = distributionData.filter(item => item.value > 0);

  // 如果没有数据，显示空状态
  if (filteredData.length === 0) {
    distributionChartOption.value = {
      title: {
        text: "审核分布",
        left: "center",
        textStyle: {
          fontSize: 16,
          color: "#333",
        },
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999'
        }
      }
    };
    return;
  }

  // 创建完整的饼图配置
  const pieConfig = {
    title: {
      text: "审核分布",
      left: "center",
      top: 10,
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: "horizontal",
      bottom: 5,
      left: "center",
      itemGap: 20,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      bottom: 60, // 为图例留出足够空间
    },
    series: [
      {
        name: "审核分布",
        type: "pie",
        radius: ["35%", "65%"],
        center: ["50%", "45%"], // 向上移动图表中心，为底部图例留空间
        data: filteredData,
        label: {
          show: true,
          formatter: "{b}: {c}",
          position: "outside",
          fontSize: 11,
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 5,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
    color: ["#e6a23c", "#67c23a", "#f56c6c"],
  };

  console.log("🥧 最终分布图表配置:", pieConfig);
  console.log("🥧 设置饼图选项...");
  distributionChartOption.value = pieConfig;
};

// 员工专用图表设置
const setupEmployeeCharts = (trendsData = null) => {
  console.log("👤 设置员工专用图表", trendsData);

  // 简化的趋势图表 - 显示提交趋势
  let months, submissionData;

  if (trendsData && Array.isArray(trendsData) && trendsData.length > 0) {
    // 使用真实数据
    months = trendsData.map(item => {
      const date = item.date || item.month || item.week;
      if (date && date.includes('-')) {
        const [year, month] = date.split('-');
        return `${parseInt(month)}月`;
      }
      return date;
    });
    submissionData = trendsData.map(item => item.total || 0);
    console.log("👤 使用真实趋势数据:", { months, submissionData });
  } else {
    // 降级到默认数据
    months = ['1月', '2月', '3月', '4月', '5月', '6月'];
    submissionData = [0, 0, 0, 0, 0, 0]; // 使用0而不是假数据
    console.log("👤 使用默认趋势数据（无真实数据）");
  }

  trendChartOption.value = {
    title: {
      text: "合同提交趋势",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "normal",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: "{b}: {c} 份合同",
    },
    xAxis: {
      type: "category",
      data: months,
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
        },
      },
      axisLabel: {
        color: "#606266",
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
        },
      },
      axisLabel: {
        color: "#606266",
      },
      splitLine: {
        lineStyle: {
          color: "#f5f7fa",
        },
      },
    },
    series: [
      {
        name: "提交数量",
        type: "line",
        data: submissionData,
        smooth: true,
        itemStyle: {
          color: "#409eff",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(64, 158, 255, 0.3)" },
              { offset: 1, color: "rgba(64, 158, 255, 0.1)" },
            ],
          },
        },
      },
    ],
  };

  // 简化的分布图表 - 显示合同状态分布
  const statusData = [
    { name: "待审核", value: pendingContracts.value },
    { name: "已完成", value: approvedContracts.value },
    { name: "已拒绝", value: rejectedContracts.value },
  ].filter(item => item.value > 0);

  distributionChartOption.value = {
    title: {
      text: "合同状态分布",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "normal",
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)",
    },
    legend: {
      bottom: "10%",
      left: "center",
      textStyle: {
        color: "#606266",
      },
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "65%"],
        center: ["50%", "45%"],
        data: statusData,
        label: {
          show: true,
          formatter: "{b}: {c}",
          position: "outside",
          fontSize: 11,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
    color: ["#e6a23c", "#67c23a", "#f56c6c"],
  };
};

// 方法
const getEfficiencyType = (efficiency) => {
  if (efficiency >= 90) return "success";
  if (efficiency >= 80) return "warning";
  return "danger";
};

// 生命周期
onMounted(() => {
  loadStatistics();
});
</script>

<style scoped>
.statistics-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  padding: 24px;
}

.legal-officer-notice {
  padding: 24px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.charts-section {
  padding: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.placeholder-text {
  font-size: 12px;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailed-stats {
  padding: 24px;
}

.notice-card {
  border: 2px solid #409EFF;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
}

.notice-icon {
  flex-shrink: 0;
  margin-top: 10px;
}

.notice-text {
  flex: 1;
}

.notice-text h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.notice-text p {
  margin: 0 0 8px 0;
  color: #606266;
  line-height: 1.6;
}

.notice-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

.notice-actions .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
